<!DOCTYPE html>
<html lang="pt-br">

<head>
  <style>
    .info-item {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 5px;
      border-left: 4px solid #0d6efd;
    }

    .info-item strong {
      color: #495057;
      display: block;
      margin-bottom: 5px;
    }

    .info-item p {
      margin: 0;
      color: #212529;
      font-weight: 500;
    }
  </style>
</head>
<body>

  <main id="main" class="main">

    <div class="pagetitle">
      <h1>Perfil do Professor</h1>
      <nav>
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="/system/Professor">Dashboard</a></li>
          <li class="breadcrumb-item active">Dados do Perfil</li>
        </ol>
      </nav>
    </div><!-- End Page Title -->

    <section class="section dashboard">
      <div class="row">

        <!-- Left side columns -->
        <div class="col-lg-8">
          <div class="row">

            <!-- Dados do Aluno Card -->
            <div class="col-12">
              <div class="card info-card">
                <div class="card-body">
                  <h5 class="card-title">Dados do Professor</h5>

                  <div class="row">
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Nome:</strong>
                        <p class="nome_professor"><%= professor ? professor.nome : 'Não informado' %></p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>CPF:</strong>
                        <p class="cpf_professor">
                          <% if (professor && professor.cpf) { %>
                            <%= professor.cpf %>
                          <% } else { %>
                            <span style="color: #6c757d; font-style: italic;">Não cadastrado</span>
                          <% } %>
                        </p>
                      </div>
                    </div>

                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Email:</strong>
                        <p class="email_professor"><%= professor ? professor.email : 'Não informado' %></p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Salário:</strong>
                        <p class="salario_professor">
                          <% if (professor && professor.salario) { %>
                            R$ <%= parseFloat(professor.salario).toLocaleString('pt-BR', {minimumFractionDigits: 2}) %>
                          <% } else { %>
                            <span style="color: #6c757d; font-style: italic;">Não informado</span>
                          <% } %>
                        </p>
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </div><!-- End Dados do Aluno Card -->

            <!-- Dados de Contato Card -->
            <div class="col-12">
              <div class="card info-card">
                <div class="card-body">
                  <h5 class="card-title">Dados de contato</h5>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Telefone:</strong>
                        <p class="telefone_professor">
                          <% if (professor && professor.telefone) { %>
                            <%= professor.telefone %>
                          <% } else { %>
                            <span style="color: #6c757d; font-style: italic;">Não informado</span>
                          <% } %>
                        </p>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>Email:</strong>
                        <p class="email_professor_contato"><%= professor ? professor.email : 'Não informado' %></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div><!-- End Dados de Contato Card -->

          </div>
        </div><!-- End Left side columns -->

        <!-- Right side columns -->
        <div class="col-lg-4">

          <!-- Editar Dados Card -->
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">Editar Dados</h5>

              <form id="form-editar-perfil" class="row g-3">
                <div class="col-12">
                  <label for="nome" class="form-label">Nome do Professor</label>
                  <input type="text" class="form-control nome_professor" id="nome" name="nome" placeholder="<%= professor ? professor.nome : 'Nome atual' %>">
                  <small class="text-muted">Deixe em branco para manter: <%= professor ? professor.nome : 'Nome atual' %></small>
                </div>

                <div class="col-12">
                  <label for="email" class="form-label">Email</label>
                  <input type="email" class="form-control email_professor" id="email" name="email" placeholder="<%= professor ? professor.email : 'Email atual' %>">
                  <small class="text-muted">Deixe em branco para manter: <%= professor ? professor.email : 'Email atual' %></small>
                </div>

                <div class="col-12">
                  <label for="cpf" class="form-label">CPF do Professor</label>
                  <input type="text" class="form-control" id="cpf" name="cpf" placeholder="<%= professor && professor.cpf ? professor.cpf : '000.000.000-00' %>" maxlength="14">
                  <small class="text-muted">
                    <% if (professor && professor.cpf) { %>
                      Deixe em branco para manter: <%= professor.cpf %>
                    <% } else { %>
                      CPF não cadastrado - digite para adicionar (formato: 000.000.000-00)
                    <% } %>
                  </small>
                </div>

                <div class="col-12">
                  <label for="telefone" class="form-label">Telefone</label>
                  <input type="tel" class="form-control telefone_professor" id="telefone" name="telefone" placeholder="<%= professor ? professor.telefone : 'Telefone atual' %>">
                  <small class="text-muted">Deixe em branco para manter: <%= professor ? professor.telefone : 'Telefone atual' %></small>
                </div>

                <div class="col-12">
                  <label for="salario" class="form-label">Salário</label>
                  <input type="number" step="0.01" class="form-control salario_professor" id="salario" name="salario" placeholder="<%= professor && professor.salario ? professor.salario : '0.00' %>">
                  <small class="text-muted">
                    <% if (professor && professor.salario) { %>
                      Deixe em branco para manter: R$ <%= parseFloat(professor.salario).toLocaleString('pt-BR', {minimumFractionDigits: 2}) %>
                    <% } else { %>
                      Salário não cadastrado - digite para adicionar
                    <% } %>
                  </small>
                </div>

                <div class="col-12">
                  <label for="nova_senha" class="form-label">Nova Senha (opcional)</label>
                  <input type="password" class="form-control" id="nova_senha" name="nova_senha" minlength="6">
                  <small class="text-muted">Deixe em branco para manter a senha atual. Mínimo 6 caracteres.</small>
                </div>

                <hr>

                <div class="col-12">
                  <div class="alert alert-warning">
                    <small><i class="bi bi-exclamation-triangle"></i> <strong>Importante:</strong> Digite sua senha atual para confirmar as alterações.</small>
                  </div>
                </div>

                <div class="col-12">
                  <label for="senha_atual" class="form-label text-danger">Senha Atual *</label>
                  <input type="password" class="form-control" id="senha_atual" name="senha_atual" required>
                  <small class="text-danger">Obrigatório para confirmar as alterações</small>
                </div>
                <div class="text-center">
                  <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                  <button type="reset" class="btn btn-secondary">Cancelar</button>
                </div>
              </form>

            </div>
          </div><!-- End Editar Dados Card -->

          <!-- Estatísticas Rápidas -->
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">Estatísticas</h5>

              <div class="d-flex align-items-center mb-3">
                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center me-3" style="background-color: #e3f2fd; color: #1976d2;">
                  <i class="bi bi-book"></i>
                </div>
                <div>
                  <h6 class="mb-0">Disciplinas</h6>
                  <small class="text-muted"><%= estatisticas ? estatisticas.disciplinas : 0 %> matérias</small>
                </div>
              </div>

              <div class="d-flex align-items-center mb-3">
                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center me-3" style="background-color: #e8f5e8; color: #388e3c;">
                  <i class="bi bi-people"></i>
                </div>
                <div>
                  <h6 class="mb-0">Turmas</h6>
                  <small class="text-muted"><%= estatisticas ? estatisticas.turmas : 0 %> turmas</small>
                </div>
              </div>

              <div class="d-flex align-items-center">
                <div class="card-icon rounded-circle d-flex align-items-center justify-content-center me-3" style="background-color: #fff3e0; color: #f57c00;">
                  <i class="bi bi-clipboard-check"></i>
                </div>
                <div>
                  <h6 class="mb-0">Atividades Criadas</h6>
                  <small class="text-muted"><%= estatisticas ? estatisticas.atividades_criadas : 0 %> atividades</small>
                </div>
              </div>

            </div>
          </div><!-- End Estatísticas Card -->

        </div><!-- End Right side columns -->

      </div>
    </section>

  </main><!-- End #main -->

  <!-- ======= Footer ======= -->
  <footer id="footer" class="footer">
    <div class="copyright">
      &copy; Copyright <strong><span>NiceAdmin</span></strong>. All Rights Reserved
      &copy; Copyright <strong><span>Devminds</span></strong>. All Rights Reserved
      &copy; Copyright <strong><span>Aurora Boreal</span></strong>. All Rights Reserved
    </div>
  </footer><!-- End Footer -->

  <a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i
      class="bi bi-arrow-up-short"></i></a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/apexcharts/apexcharts.min.js"></script>
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/chart.js/chart.umd.js"></script>
  <script src="assets/vendor/echarts/echarts.min.js"></script>
  <script src="assets/vendor/quill/quill.js"></script>
  <script src="assets/vendor/simple-datatables/simple-datatables.js"></script>
  <script src="assets/vendor/tinymce/tinymce.min.js"></script>
  <script src="assets/vendor/php-email-form/validate.js"></script>

  <!-- Template Main JS File -->
  <script src="assets/js/main.js"></script>

  <!-- Script para o formulário de edição -->
  <script>
    document.getElementById('form-editar-perfil').addEventListener('submit', function(e) {
      e.preventDefault();

      // Verificar se a senha atual foi fornecida
      const senhaAtual = document.getElementById('senha_atual').value;
      if (!senhaAtual) {
        alert('Por favor, digite sua senha atual para confirmar as alterações.');
        return;
      }

      // Coletar dados do formulário
      const formData = new FormData(this);
      const dados = Object.fromEntries(formData);

      // Mostrar loading
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Salvando...';
      submitBtn.disabled = true;

      // Enviar dados para o servidor
      fetch('/system/professores/perfil/atualizar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(dados)
      })
      .then(response => response.json())
      .then(result => {
        if (result.sucesso) {
          alert(result.mensagem);
          // Recarregar a página para mostrar os dados atualizados
          location.reload();
        } else {
          alert('Erro: ' + result.mensagem);
        }
      })
      .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao salvar alterações. Tente novamente.');
      })
      .finally(() => {
        // Restaurar botão
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
      });
    });
  </script>

</body>

</html>
