<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title><PERSON><PERSON><PERSON><PERSON></title>
    <meta content="" name="description">
    <meta content="" name="keywords">

    <!-- Favicons -->
    <link href="img/AURORALOGOSFUNDO.ico" rel="icon">
    <link href="img/AURORALOGOSFUNDO.ico" rel="apple-touch-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.gstatic.com" rel="preconnect">
    <link
        href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Nunito:300,300i,400,400i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i"
        rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="assets/vendor/quill/quill.snow.css" rel="stylesheet">
    <link href="assets/vendor/quill/quill.bubble.css" rel="stylesheet">
    <link href="assets/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="assets/vendor/simple-datatables/style.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- =======================================================
  * Template Name: NiceAdmin
  * Template URL: https://bootstrapmade.com/nice-admin-bootstrap-admin-html-template/
  * Updated: Apr 20 2024 with Bootstrap v5.3.3
  * Author: BootstrapMade.com
  * License: https://bootstrapmade.com/license/
  ======================================================== -->
</head>

<body>

    <!-- ======= Header ======= -->
    <header id="header" class="header fixed-top d-flex align-items-center">

        <div class="d-flex align-items-center justify-content-between">
            <a href="/" class="logo d-flex align-items-center">
                <img src="img/AURORALOGOSFUNDO.ico" alt="">
                <span class="d-none d-lg-block">Aurora Boreal</span>
            </a>
            <i class="bi bi-list toggle-sidebar-btn"></i>
        </div><!-- End Logo -->




        </ul>
        </nav><!-- End Icons Navigation -->

    </header><!-- End Header -->

    <main id="main" class="main">

        <div class="pagetitle">
            <h1>Painéis e gerenciamentos</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Pág.inicial</a></li>
                    <li class="breadcrumb-item active">Painéis</li>
                </ol>
            </nav>
        </div><!-- End Page Title -->

        <section class="section dashboard">
            <div class="row">

                <!-- Left side columns -->
                <div class="col-lg-8">
                    <div class="row">






                                   <!-- Perfil Card com Link -->
<div class="col-xxl-4 col-md-6">
  <a href="/system/professores/perfil" style="text-decoration: none; cursor: pointer;">
    <div class="card info-card revenue-card">
      <div class="card-body">
        <h5 class="card-title">Perfil <span></span></h5>

        <div class="d-flex align-items-center">
          <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
            <i class="bi bi-person-circle"></i>
          </div>
          <div class="ps-3">
            <h6><%= rows[0].professor.nome %></h6>
          </div>
        </div>
      </div>
    </div>
  </a>
</div>

<!-- End Perfil Card -->





                    </div>
                </div><!-- End Left side columns -->

                <!-- Right side columns -->
                <div class="col-lg-4">
                    <!-- Multi Columns Form -->
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Registro de Uso De Material</h5>
                            <form id="form-material" class="row g-3">
                                <div class="col-md-12">
                                    <label for="material-select" class="form-label">Material:</label>
                                    <select class="form-select" id="material-select" name="produto_id" required>
                                        <option value="" selected disabled>Selecione um material</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="quantidade" class="form-label">Quantidade: </label>
                                    <input type="number" class="form-control" id="quantidade" name="quantidade_usada" min="1" required>
                                </div>
                                <div class="col-md-12">
                                    <label for="observacoes" class="form-label">Observações (opcional):</label>
                                    <textarea class="form-control" id="observacoes" name="observacoes" rows="2"></textarea>
                                </div>
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary">Registrar Uso</button>
                                    <button type="reset" class="btn btn-secondary">Limpar</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <style>
                        /* Remove espaço vazio do meio da página */
                        .col-lg-8 > .row {
                            min-height: 0 !important;
                            height: auto !important;
                        }
                        @media (min-width: 992px) {
                            .col-lg-8 {
                                flex: 0 0 0%;
                                max-width: 0%;
                                padding: 0;
                                margin: 0;
                                display: none;
                            }
                            .col-lg-4 {
                                flex: 0 0 100%;
                                max-width: 100%;
                            }
                        }
                    </style>
                </div><!-- End Multi Columns Form -->



                <!-- Table with stripped rows -->



                
                
                <!--fim de visualizar respostas-->
            </div><!-- End Right side columns -->
            </div>
        </section>

    </main><!-- End #main -->

    <!-- ======= Footer ======= -->
    <footer id="footer" class="footer">
        <div class="copyright">
            &copy; Copyright <strong><span>NiceAdmin</span></strong>. All Rights Reserved
            &copy; Copyright <strong><span>Devminds</span></strong>. All Rights Reserved
            &copy; Copyright <strong><span>Aurora Boreal</span></strong>. All Rights Reserved
        </div>

    </footer><!-- End Footer -->

    <a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i
            class="bi bi-arrow-up-short"></i></a>

    <!-- Vendor JS Files -->
    <script src="assets/vendor/apexcharts/apexcharts.min.js"></script>
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/vendor/chart.js/chart.umd.js"></script>
    <script src="assets/vendor/echarts/echarts.min.js"></script>
    <script src="assets/vendor/quill/quill.js"></script>
    <script src="assets/vendor/simple-datatables/simple-datatables.js"></script>
    <script src="assets/vendor/tinymce/tinymce.min.js"></script>
    <script src="assets/vendor/php-email-form/validate.js"></script>

    <!-- Template Main JS File -->
    <script src="assets/js/main.js"></script>

    <!-- Script para Gerenciamento de Materiais -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            carregarMateriaisDisponiveis();

            // Event listener para o formulário
            document.getElementById('form-material').addEventListener('submit', function(e) {
                e.preventDefault();
                registrarUsoMaterial();
            });
        });

        function carregarMateriaisDisponiveis() {
            console.log('Iniciando carregamento de materiais...');
            fetch('/system/professores/testeMateriais', {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include'
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Dados recebidos:', data);
                const select = document.getElementById('material-select');
                select.innerHTML = '<option value="" selected disabled>Selecione um material</option>';

                // Usar data diretamente (array de materiais)
                const materiais = data;

                if (materiais && Array.isArray(materiais) && materiais.length > 0) {
                    materiais.forEach(material => {
                        const option = document.createElement('option');
                        option.value = material.id;
                        option.textContent = `${material.nome_produto} (Estoque: ${material.quantidade})`;
                        option.setAttribute('data-estoque', material.quantidade);
                        select.appendChild(option);
                    });
                    console.log('Materiais carregados com sucesso');
                } else {
                    select.innerHTML += '<option disabled>Nenhum material disponível</option>';
                    console.log('Nenhum material encontrado');
                }
            })
            .catch(error => {
                console.error('Erro ao carregar materiais:', error);
                alert('Erro ao carregar materiais disponíveis: ' + error.message);
            });
        }

        function registrarUsoMaterial() {
            const formData = new FormData(document.getElementById('form-material'));
            const data = Object.fromEntries(formData);

            // Validar estoque
            const select = document.getElementById('material-select');
            const selectedOption = select.options[select.selectedIndex];
            const estoqueDisponivel = parseInt(selectedOption.getAttribute('data-estoque'));
            const quantidadeSolicitada = parseInt(data.quantidade_usada);

            if (quantidadeSolicitada > estoqueDisponivel) {
                alert(`Quantidade solicitada (${quantidadeSolicitada}) é maior que o estoque disponível (${estoqueDisponivel})`);
                return;
            }

            fetch('/system/professores/registrarUsoMaterial', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.sucesso) {
                    alert(result.mensagem);
                    document.getElementById('form-material').reset();
                    carregarMateriaisDisponiveis(); // Recarregar para atualizar estoque
                } else {
                    alert(result.mensagem);
                }
            })
            .catch(error => {
                console.error('Erro ao registrar uso:', error);
                alert('Erro ao registrar uso do material');
            });
        }


    </script>

</body>

</html>